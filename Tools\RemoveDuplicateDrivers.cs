using System;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Tools
{
    /// <summary>
    /// أداة لحذف السائقين المكررين من قاعدة البيانات نهائياً
    /// </summary>
    public static class RemoveDuplicateDrivers
    {
        /// <summary>
        /// حذف السائقين المكررين من قاعدة البيانات
        /// </summary>
        public static async Task<int> RemoveDuplicatesAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                Console.WriteLine("🔄 بدء فحص وحذف السائقين المكررين...");

                // جلب جميع السائقين
                var allDrivers = await context.Drivers.ToListAsync();
                Console.WriteLine($"📊 تم جلب {allDrivers.Count} سائق من قاعدة البيانات");

                if (allDrivers.Count == 0)
                {
                    Console.WriteLine("ℹ️ لا توجد سائقين في قاعدة البيانات");
                    return 0;
                }

                // تجميع السائقين حسب الكود
                var groupedByCode = allDrivers
                    .Where(d => !string.IsNullOrEmpty(d.DriverCode))
                    .GroupBy(d => d.DriverCode)
                    .Where(g => g.Count() > 1) // فقط المجموعات التي تحتوي على أكثر من سائق
                    .ToList();

                Console.WriteLine($"🔍 تم العثور على {groupedByCode.Count} مجموعة سائقين مكررين");

                int removedCount = 0;

                foreach (var group in groupedByCode)
                {
                    var drivers = group.OrderByDescending(d => d.Id).ToList(); // ترتيب حسب ID (الأحدث أولاً)
                    var keepDriver = drivers.First(); // الاحتفاظ بالسائق الأحدث
                    var duplicates = drivers.Skip(1).ToList(); // السائقين المكررين للحذف

                    Console.WriteLine($"🔧 معالجة المجموعة للكود: {group.Key}");
                    Console.WriteLine($"   - الاحتفاظ بـ: {keepDriver.Name} (ID: {keepDriver.Id})");
                    
                    foreach (var duplicate in duplicates)
                    {
                        Console.WriteLine($"   - حذف: {duplicate.Name} (ID: {duplicate.Id})");
                        context.Drivers.Remove(duplicate);
                        removedCount++;
                    }
                }

                // تجميع السائقين حسب الاسم (للسائقين بدون كود أو بكود فارغ)
                var driversWithoutCode = allDrivers
                    .Where(d => string.IsNullOrEmpty(d.DriverCode))
                    .ToList();

                if (driversWithoutCode.Any())
                {
                    var groupedByName = driversWithoutCode
                        .GroupBy(d => d.Name.Trim().ToLower())
                        .Where(g => g.Count() > 1)
                        .ToList();

                    Console.WriteLine($"🔍 تم العثور على {groupedByName.Count} مجموعة سائقين مكررين بدون كود");

                    foreach (var group in groupedByName)
                    {
                        var drivers = group.OrderByDescending(d => d.Id).ToList();
                        var keepDriver = drivers.First();
                        var duplicates = drivers.Skip(1).ToList();

                        Console.WriteLine($"🔧 معالجة المجموعة للاسم: {group.Key}");
                        Console.WriteLine($"   - الاحتفاظ بـ: {keepDriver.Name} (ID: {keepDriver.Id})");
                        
                        foreach (var duplicate in duplicates)
                        {
                            Console.WriteLine($"   - حذف: {duplicate.Name} (ID: {duplicate.Id})");
                            context.Drivers.Remove(duplicate);
                            removedCount++;
                        }
                    }
                }

                if (removedCount > 0)
                {
                    await context.SaveChangesAsync();
                    Console.WriteLine($"✅ تم حذف {removedCount} سائق مكرر من قاعدة البيانات");
                }
                else
                {
                    Console.WriteLine("ℹ️ لا توجد سائقين مكررين للحذف");
                }

                // طباعة الإحصائيات النهائية
                var finalCount = await context.Drivers.CountAsync();
                Console.WriteLine($"📊 العدد النهائي للسائقين: {finalCount}");

                return removedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في حذف السائقين المكررين: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// فحص السائقين المكررين بدون حذف
        /// </summary>
        public static async Task<int> CheckDuplicatesAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                Console.WriteLine("🔍 فحص السائقين المكررين...");

                var allDrivers = await context.Drivers.ToListAsync();
                Console.WriteLine($"📊 إجمالي السائقين: {allDrivers.Count}");

                // فحص التكرار حسب الكود
                var duplicatesByCode = allDrivers
                    .Where(d => !string.IsNullOrEmpty(d.DriverCode))
                    .GroupBy(d => d.DriverCode)
                    .Where(g => g.Count() > 1)
                    .ToList();

                Console.WriteLine($"🔍 مجموعات مكررة حسب الكود: {duplicatesByCode.Count}");

                int totalDuplicates = 0;
                foreach (var group in duplicatesByCode)
                {
                    var count = group.Count();
                    totalDuplicates += count - 1; // عدد المكررين (ناقص الأصلي)
                    Console.WriteLine($"   - الكود {group.Key}: {count} سائق");
                }

                // فحص التكرار حسب الاسم (للسائقين بدون كود)
                var driversWithoutCode = allDrivers
                    .Where(d => string.IsNullOrEmpty(d.DriverCode))
                    .ToList();

                var duplicatesByName = driversWithoutCode
                    .GroupBy(d => d.Name.Trim().ToLower())
                    .Where(g => g.Count() > 1)
                    .ToList();

                Console.WriteLine($"🔍 مجموعات مكررة حسب الاسم (بدون كود): {duplicatesByName.Count}");

                foreach (var group in duplicatesByName)
                {
                    var count = group.Count();
                    totalDuplicates += count - 1;
                    Console.WriteLine($"   - الاسم {group.Key}: {count} سائق");
                }

                Console.WriteLine($"📊 إجمالي السائقين المكررين: {totalDuplicates}");
                return totalDuplicates;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في فحص السائقين المكررين: {ex.Message}");
                throw;
            }
        }
    }
}
