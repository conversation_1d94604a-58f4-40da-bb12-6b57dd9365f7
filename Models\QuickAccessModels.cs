using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج بيانات السائق في الميدان - للوصول السريع
    /// </summary>
    public class ActiveDriverInfo : INotifyPropertyChanged
    {
        private bool _isSelected;

        public int DriverId { get; set; }
        public string DriverCode { get; set; } = string.Empty;
        public string DriverName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string DriverPhone { get; set; } = string.Empty;
        public string VehicleType { get; set; } = string.Empty;
        public string VehicleNumber { get; set; } = string.Empty;

        // معلومات التكليف الحالي
        public string VisitNumber { get; set; } = string.Empty;
        public string MissionPurpose { get; set; } = string.Empty;
        public DateTime DepartureDate { get; set; }
        public DateTime ReturnDate { get; set; }
        public int DaysCount { get; set; }

        // معلومات المشاريع
        public List<string> ProjectNames { get; set; } = new List<string>();
        public string ProjectsText { get; set; } = string.Empty;

        // القائمين بالزيارة
        public List<string> VisitorNames { get; set; } = new List<string>();
        public string VisitorsText { get; set; } = string.Empty;

        // التواريخ المنسقة
        public string DepartureDateText { get; set; } = string.Empty;
        public string ReturnDateText { get; set; } = string.Empty;
        public string DateRangeText => $"من: {DepartureDateText} حتى: {ReturnDateText}";

        // حالة التكليف
        public string Status { get; set; } = "في الميدان";
        public string StatusColor { get; set; } = "#4CAF50";

        // أيام متبقية
        public int RemainingDays { get; set; }

        public string RemainingDaysText { get; set; } = string.Empty;

        // خصائص للواجهة
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// إحصائيات الوصول السريع
    /// </summary>
    public class QuickAccessStatistics
    {
        public int TotalActiveDrivers { get; set; }
        public int DriversInField { get; set; }
        public int ActiveVisits { get; set; }
        public int EndingToday { get; set; }
        public int EndingTomorrow { get; set; }

        public string TotalActiveDriversText => $"{TotalActiveDrivers} سائق نشط";
        public string DriversInFieldText => $"{DriversInField} في الميدان";
        public string ActiveVisitsText => $"{ActiveVisits} زيارة نشطة";
        public string EndingTodayText => $"{EndingToday} ينتهي اليوم";
        public string EndingTomorrowText => $"{EndingTomorrow} ينتهي غداً";
    }

    /// <summary>
    /// فلتر البحث للوصول السريع
    /// </summary>
    public class QuickAccessFilter
    {
        public string SearchText { get; set; } = string.Empty;
        public bool ShowOnlyInField { get; set; } = true;
        public bool ShowEndingToday { get; set; } = false;
        public bool ShowEndingTomorrow { get; set; } = false;
        public DateTime? FilterDate { get; set; }
    }
}
