using System;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace SFDSystem.Migrations
{
    /// <summary>
    /// Migration لتصحيح أسماء السائقين في جدول DriverQuotes
    /// </summary>
    public static class FixDriverNamesInQuotes
    {
        /// <summary>
        /// تطبيق Migration لتصحيح أسماء السائقين
        /// </summary>
        public static async Task<bool> ApplyMigrationAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء تصحيح أسماء السائقين في جدول DriverQuotes...");

                // جلب جميع السائقين من الجدول الرئيسي
                var allDrivers = await context.Drivers.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"📊 تم جلب {allDrivers.Count} سائق من الجدول الرئيسي");

                // جلب جميع العروض التي تحتاج تصحيح
                var allQuotes = await context.DriverQuotes.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"📊 تم جلب {allQuotes.Count} عرض من جدول DriverQuotes");

                int fixedCount = 0;

                foreach (var quote in allQuotes)
                {
                    if (string.IsNullOrEmpty(quote.DriverName))
                        continue;

                    // البحث عن السائق الصحيح
                    var correctDriver = FindCorrectDriver(allDrivers, quote.DriverName);
                    
                    if (correctDriver != null && correctDriver.Name != quote.DriverName)
                    {
                        var oldName = quote.DriverName;
                        quote.DriverName = correctDriver.Name;
                        
                        System.Diagnostics.Debug.WriteLine($"🔧 تصحيح الاسم: '{oldName}' -> '{correctDriver.Name}'");
                        fixedCount++;
                    }
                }

                if (fixedCount > 0)
                {
                    await context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم تصحيح {fixedCount} اسم سائق في جدول DriverQuotes");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ لا توجد أسماء تحتاج تصحيح");
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تصحيح أسماء السائقين: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// البحث عن السائق الصحيح بناءً على الاسم المقطوع
        /// </summary>
        private static DriverManagementSystem.Models.Driver FindCorrectDriver(
            System.Collections.Generic.List<DriverManagementSystem.Models.Driver> allDrivers, 
            string partialName)
        {
            if (string.IsNullOrEmpty(partialName))
                return null;

            var cleanPartialName = partialName.Trim();

            // مطابقة دقيقة أولاً
            var exactMatch = allDrivers.FirstOrDefault(d => 
                d.Name.Equals(cleanPartialName, StringComparison.OrdinalIgnoreCase));
            if (exactMatch != null)
                return exactMatch;

            // مطابقة جزئية ذكية
            var partialWords = cleanPartialName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (partialWords.Length >= 2)
            {
                var bestMatch = allDrivers.FirstOrDefault(d =>
                {
                    if (string.IsNullOrEmpty(d.Name))
                        return false;

                    var driverWords = d.Name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                    
                    // إذا كان اسم السائق يحتوي على كلمات أكثر من الاسم المقطوع
                    if (driverWords.Length > partialWords.Length)
                    {
                        int matchCount = 0;
                        foreach (var partialWord in partialWords)
                        {
                            if (driverWords.Any(dw => dw.Equals(partialWord, StringComparison.OrdinalIgnoreCase)))
                                matchCount++;
                        }
                        
                        // إذا تطابقت جميع كلمات الاسم المقطوع
                        return matchCount == partialWords.Length;
                    }

                    return false;
                });

                if (bestMatch != null)
                    return bestMatch;
            }

            // مطابقة تقليدية كاحتياط
            return allDrivers.FirstOrDefault(d => 
                !string.IsNullOrEmpty(d.Name) && 
                (d.Name.Contains(cleanPartialName, StringComparison.OrdinalIgnoreCase) ||
                 cleanPartialName.Contains(d.Name, StringComparison.OrdinalIgnoreCase)));
        }
    }
}
