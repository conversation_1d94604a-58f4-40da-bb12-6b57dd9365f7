using System;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Tools
{
    /// <summary>
    /// أداة لتصحيح أسماء السائقين في قاعدة البيانات مباشرة
    /// </summary>
    public static class FixDriverNamesDirectly
    {
        /// <summary>
        /// تصحيح أسماء السائقين في جدول DriverQuotes
        /// </summary>
        public static async Task FixDriverNamesAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                Console.WriteLine("🔄 بدء تصحيح أسماء السائقين...");

                // جلب جميع السائقين من الجدول الرئيسي
                var allDrivers = await context.Drivers.ToListAsync();
                Console.WriteLine($"📊 تم جلب {allDrivers.Count} سائق من الجدول الرئيسي");

                // جلب جميع العروض
                var allQuotes = await context.DriverQuotes.ToListAsync();
                Console.WriteLine($"📊 تم جلب {allQuotes.Count} عرض من جدول DriverQuotes");

                int fixedCount = 0;

                foreach (var quote in allQuotes)
                {
                    if (string.IsNullOrEmpty(quote.DriverName))
                        continue;

                    var originalName = quote.DriverName;
                    
                    // البحث عن السائق الصحيح
                    var correctDriver = FindCorrectDriver(allDrivers, quote.DriverName);
                    
                    if (correctDriver != null && correctDriver.Name != quote.DriverName)
                    {
                        quote.DriverName = correctDriver.Name;
                        
                        Console.WriteLine($"🔧 تصحيح: '{originalName}' -> '{correctDriver.Name}'");
                        fixedCount++;
                    }
                    else
                    {
                        Console.WriteLine($"ℹ️ لم يتم العثور على تطابق لـ: '{originalName}'");
                    }
                }

                if (fixedCount > 0)
                {
                    await context.SaveChangesAsync();
                    Console.WriteLine($"✅ تم تصحيح {fixedCount} اسم سائق بنجاح");
                }
                else
                {
                    Console.WriteLine("ℹ️ لا توجد أسماء تحتاج تصحيح");
                }

                // طباعة عينة من البيانات المصححة
                var sampleQuotes = await context.DriverQuotes.Take(5).ToListAsync();
                Console.WriteLine("\n📋 عينة من البيانات بعد التصحيح:");
                foreach (var quote in sampleQuotes)
                {
                    Console.WriteLine($"   - {quote.DriverName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تصحيح أسماء السائقين: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن السائق الصحيح بناءً على الاسم المقطوع
        /// </summary>
        private static DriverManagementSystem.Models.Driver FindCorrectDriver(
            System.Collections.Generic.List<DriverManagementSystem.Models.Driver> allDrivers, 
            string partialName)
        {
            if (string.IsNullOrEmpty(partialName))
                return null;

            var cleanPartialName = partialName.Trim();

            // مطابقة دقيقة أولاً
            var exactMatch = allDrivers.FirstOrDefault(d => 
                d.Name.Equals(cleanPartialName, StringComparison.OrdinalIgnoreCase));
            if (exactMatch != null)
                return exactMatch;

            // مطابقة جزئية ذكية
            var partialWords = cleanPartialName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (partialWords.Length >= 2)
            {
                var bestMatch = allDrivers.FirstOrDefault(d =>
                {
                    if (string.IsNullOrEmpty(d.Name))
                        return false;

                    var driverWords = d.Name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                    
                    // إذا كان اسم السائق يحتوي على كلمات أكثر من الاسم المقطوع
                    if (driverWords.Length > partialWords.Length)
                    {
                        int matchCount = 0;
                        foreach (var partialWord in partialWords)
                        {
                            if (driverWords.Any(dw => dw.Equals(partialWord, StringComparison.OrdinalIgnoreCase)))
                                matchCount++;
                        }
                        
                        // إذا تطابقت جميع كلمات الاسم المقطوع
                        return matchCount == partialWords.Length;
                    }

                    return false;
                });

                if (bestMatch != null)
                    return bestMatch;
            }

            // مطابقة تقليدية كاحتياط
            return allDrivers.FirstOrDefault(d => 
                !string.IsNullOrEmpty(d.Name) && 
                (d.Name.Contains(cleanPartialName, StringComparison.OrdinalIgnoreCase) ||
                 cleanPartialName.Contains(d.Name, StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// طباعة إحصائيات البيانات
        /// </summary>
        public static async Task PrintDataStatisticsAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                var driversCount = await context.Drivers.CountAsync();
                var quotesCount = await context.DriverQuotes.CountAsync();
                
                Console.WriteLine($"\n📊 إحصائيات البيانات:");
                Console.WriteLine($"   - عدد السائقين: {driversCount}");
                Console.WriteLine($"   - عدد العروض: {quotesCount}");
                
                // عرض عينة من أسماء السائقين
                var sampleDrivers = await context.Drivers.Take(5).ToListAsync();
                Console.WriteLine($"\n👥 عينة من أسماء السائقين:");
                foreach (var driver in sampleDrivers)
                {
                    Console.WriteLine($"   - {driver.Name} (كود: {driver.DriverCode})");
                }
                
                // عرض عينة من العروض
                var sampleQuotes = await context.DriverQuotes.Take(5).ToListAsync();
                Console.WriteLine($"\n💰 عينة من العروض:");
                foreach (var quote in sampleQuotes)
                {
                    Console.WriteLine($"   - {quote.DriverName} - {quote.QuotedPrice:N0} ريال");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في طباعة الإحصائيات: {ex.Message}");
            }
        }
    }
}
