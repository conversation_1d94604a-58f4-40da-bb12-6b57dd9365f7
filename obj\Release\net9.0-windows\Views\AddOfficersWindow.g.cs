﻿#pragma checksum "..\..\..\..\Views\AddOfficersWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "225A2DC769FA503BCAFE0910FD09FB883CE9E173"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// AddOfficersWindow
    /// </summary>
    public partial class AddOfficersWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 93 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalOfficersText;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AddedOfficersText;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SkippedOfficersText;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusIcon;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogTextBlock;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartButton;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\AddOfficersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewOfficersButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/addofficerswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddOfficersWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalOfficersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AddedOfficersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.SkippedOfficersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 5:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.StatusIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LogTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.StartButton = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\Views\AddOfficersWindow.xaml"
            this.StartButton.Click += new System.Windows.RoutedEventHandler(this.StartButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ViewOfficersButton = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\..\Views\AddOfficersWindow.xaml"
            this.ViewOfficersButton.Click += new System.Windows.RoutedEventHandler(this.ViewOfficersButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 169 "..\..\..\..\Views\AddOfficersWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

