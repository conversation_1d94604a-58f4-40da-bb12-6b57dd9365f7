using System;
using System.Threading.Tasks;
using DriverManagementSystem.Tools;

namespace DriverManagementSystem
{
    /// <summary>
    /// تطبيق console لتنظيف السائقين المكررين
    /// </summary>
    class CleanupDriversConsole
    {
        static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🚗 أداة تنظيف السائقين المكررين");
            Console.WriteLine(new string('=', 60));
            
            try
            {
                Console.WriteLine("اختر العملية المطلوبة:");
                Console.WriteLine("1. فحص السائقين المكررين فقط");
                Console.WriteLine("2. حذف السائقين المكررين");
                Console.WriteLine("3. خروج");
                Console.Write("اختيارك (1-3): ");
                
                var choice = Console.ReadLine();
                
                switch (choice)
                {
                    case "1":
                        await CleanupDuplicateDrivers.CheckDuplicatesAsync();
                        break;
                        
                    case "2":
                        Console.WriteLine("⚠️ تحذير: هذه العملية ستحذف السائقين المكررين نهائياً!");
                        Console.Write("هل تريد المتابعة؟ (y/n): ");
                        var confirm = Console.ReadLine();
                        
                        if (confirm?.ToLower() == "y" || confirm?.ToLower() == "yes")
                        {
                            await CleanupDuplicateDrivers.RunCleanupAsync();
                        }
                        else
                        {
                            Console.WriteLine("تم إلغاء العملية");
                        }
                        break;
                        
                    case "3":
                        Console.WriteLine("👋 وداعاً!");
                        return;
                        
                    default:
                        Console.WriteLine("❌ اختيار غير صحيح");
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ عام: {ex.Message}");
            }
            
            Console.WriteLine("\nاضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}
