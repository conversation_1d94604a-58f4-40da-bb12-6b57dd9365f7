@echo off
chcp 65001 >nul
echo ===============================================
echo    🧪 اختبار الأيقونات المضمنة في النظام
echo ===============================================
echo.

echo ℹ️  الأيقونات الآن مضمنة داخل ملف التطبيق نفسه
echo ✅ لا حاجة لملفات خارجية - سيعمل في أي جهاز!
echo.

echo 🔍 فحص النسخة الجديدة...
if exist "publish_new\SFDSystem.exe" (
    echo ✅ تم العثور على النظام: publish_new\SFDSystem.exe
) else (
    echo ❌ لم يتم العثور على النظام!
    pause
    exit /b 1
)

echo.
echo 🚀 تشغيل النظام من المجلد الجديد...
cd publish_new
start SFDSystem.exe
cd ..

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo 📝 تحقق من ظهور الشعار في النوافذ والتقارير
echo 🎯 الشعار الآن مضمن داخل التطبيق ولن يختفي في أي جهاز!
echo.
pause
